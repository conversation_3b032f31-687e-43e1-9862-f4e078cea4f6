/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */


pluginManagement {
  repositories {
    /*
     *  Temporary repository to host the improved version of the
     *  com.github.hierynomus.license plugin. Can be removed when an
     *  official version with the fix is release upstream
     */
    maven {
      url = uri('https://raw.githubusercontent.com/ConsenSys/license-gradle-plugin-fix-artifacts/main/')
      content {
        includeGroup('com.github.hierynomus.license')
        includeGroup('com.hierynomus.gradle.plugins')
      }
    }
    gradlePluginPortal()
  }
}

rootProject.name='besu'
include 'acceptance-tests:test-plugins'
include 'acceptance-tests:dsl'
include 'acceptance-tests:tests'
include 'besu'
include 'config'
include 'consensus:clique'
include 'consensus:common'
include 'consensus:ibft'
include 'consensus:merge'
include 'consensus:qbft'
include 'datatypes'
include 'crypto:algorithms'
include 'crypto:services'
include 'datatypes'
include 'enclave'
include 'evm'
include 'errorprone-checks'
include 'ethereum:api'
include 'ethereum:blockcreation'
include 'ethereum:core'
include 'ethereum:eth'
include 'ethereum:evmtool'
include 'ethereum:mock-p2p'
include 'ethereum:p2p'
include 'ethereum:permissioning'
include 'ethereum:referencetests'
include 'ethereum:retesteth'
include 'ethereum:rlp'
include 'ethereum:stratum'
include 'ethereum:ethstats'
include 'ethereum:trie'
include 'ethereum:verkletrie'
include 'evm'
include 'metrics:core'
include 'metrics:rocksdb'
include 'nat'
include 'pki'
include 'plugin-api'
include 'plugins:rocksdb'
include 'privacy-contracts'
include 'services:kvstore'
include 'services:pipeline'
include 'services:tasks'
include 'testutil'
include 'util'
