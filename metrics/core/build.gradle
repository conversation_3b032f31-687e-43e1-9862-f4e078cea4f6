/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

apply plugin: 'java-library'

jar {
  archiveBaseName = 'besu-metrics-core'
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion()
      )
  }
}

publishing {
  publications {
    mavenJava(MavenPublication) { artifactId 'metrics-core' }
  }
}


dependencies {
  implementation project(':plugin-api')
  api 'org.slf4j:slf4j-api'

  implementation 'com.google.guava:guava'
  implementation 'com.google.dagger:dagger'
  implementation 'info.picocli:picocli'
  implementation 'io.grpc:grpc-netty'
  implementation 'io.grpc:grpc-core'
  implementation 'io.netty:netty-tcnative-boringssl-static'
  implementation 'io.netty:netty-transport-native-epoll'
  implementation 'io.netty:netty-all'
  implementation 'io.opentelemetry:opentelemetry-api'
  implementation 'io.opentelemetry:opentelemetry-sdk'
  implementation 'io.opentelemetry:opentelemetry-semconv'
  implementation 'io.opentelemetry:opentelemetry-sdk-trace'
  implementation 'io.opentelemetry:opentelemetry-sdk-metrics'
  implementation 'io.opentelemetry:opentelemetry-exporter-otlp'
  implementation 'io.opentelemetry:opentelemetry-sdk-extension-autoconfigure'

  implementation 'io.prometheus:simpleclient'
  implementation 'io.prometheus:simpleclient_common'
  implementation 'io.prometheus:simpleclient_hotspot'
  implementation 'io.prometheus:simpleclient_pushgateway'
  implementation 'io.vertx:vertx-core'
  implementation 'io.vertx:vertx-web'

  // test dependencies.
  testImplementation project(':util')

  testImplementation 'com.squareup.okhttp3:okhttp'
  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'

  testSupportImplementation 'org.mockito:mockito-core'

  annotationProcessor 'com.google.dagger:dagger-compiler'
}


artifacts { testSupportArtifacts testSupportJar }
