/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.nat.kubernetes.service;

/** The enum Kubernetes service type. */
public enum KubernetesServiceType {
  /** Cluster ip kubernetes service type. */
  CLUSTER_IP("ClusterIP"),
  /** Load balancer kubernetes service type. */
  LOAD_BALANCER("LoadBalancer"),
  /** Unknown kubernetes service type. */
  UNKNOWN("");

  /** The Name. */
  String name;

  KubernetesServiceType(final String name) {
    this.name = name;
  }

  /**
   * Map KubernetesServiceType from String value.
   *
   * @param name the name
   * @return the kubernetes service type
   */
  public static KubernetesServiceType fromName(final String name) {
    for (KubernetesServiceType value : values()) {
      if (value.name.equals(name)) {
        return value;
      }
    }
    return UNKNOWN;
  }
}
