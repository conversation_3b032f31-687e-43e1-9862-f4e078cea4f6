/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

apply plugin: 'java-library'

jar {
  archiveBaseName = 'besu-pki'
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion()
      )
  }
}

dependencies {
  api 'org.slf4j:slf4j-api'

  implementation 'com.google.guava:guava'
  implementation 'io.tmio:tuweni-bytes'
  implementation 'org.bouncycastle:bcpkix-jdk18on'

  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'
}

configurations { testArtifacts }
tasks.register('testJar', Jar) {
  archiveBaseName = "${project.name}-test"
  from sourceSets.test.output
}

artifacts { testArtifacts testJar }
