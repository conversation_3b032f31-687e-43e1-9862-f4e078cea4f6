/*
 * Copyright 2019 ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

apply plugin: 'java-library'

jar {
  archiveBaseName = 'besu-plugin-rocksdb'
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion()
      )
  }
}

publishing {
  publications {
    mavenJava(MavenPublication) { artifactId 'plugins-rocksdb' }
  }
}

dependencies {
  api project(':plugin-api')
  api 'org.slf4j:slf4j-api'

  implementation project(':metrics:core')
  implementation project(':metrics:rocksdb')
  implementation project(':services:kvstore')
  implementation project(':util')

  implementation 'com.fasterxml.jackson.core:jackson-databind'
  implementation 'com.google.guava:guava'
  implementation 'info.picocli:picocli'
  implementation 'io.opentelemetry:opentelemetry-api'
  implementation 'io.prometheus:simpleclient'
  implementation 'io.tmio:tuweni-bytes'
  implementation 'org.rocksdb:rocksdbjni'
  implementation project(path: ':ethereum:core')

  testImplementation project(':testutil')

  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'
  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'

  testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
}
