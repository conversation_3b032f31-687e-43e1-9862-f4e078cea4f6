/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

apply plugin: 'java-library'

jar {
  archiveBaseName = 'besu-api'
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion()
      )
  }
}

configurations { testResourceGeneration.extendsFrom testImplementation }

dependencies {
  api 'org.slf4j:slf4j-api'
  api 'org.apache.logging.log4j:log4j-api'

  implementation project(':config')
  implementation project(':consensus:merge')
  implementation project(':crypto:services')
  implementation project(':datatypes')
  implementation project(':enclave')
  implementation project(':ethereum:blockcreation')
  implementation project(':ethereum:core')
  implementation project(':ethereum:eth')
  implementation project(':ethereum:p2p')
  implementation project(':ethereum:permissioning')
  implementation project(':ethereum:rlp')
  implementation project(':ethereum:trie')
  implementation project(':evm')
  implementation project(':metrics:core')
  implementation project(':nat')
  implementation project(':plugin-api')
  implementation project(':util')
  implementation project(':services:pipeline')
  implementation project(':services:tasks')

  implementation 'com.google.guava:guava'
  implementation 'com.graphql-java:graphql-java'
  implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jdk8'
  implementation 'io.opentelemetry:opentelemetry-api'
  implementation 'io.opentelemetry:opentelemetry-extension-trace-propagators'
  implementation 'io.vertx:vertx-auth-jwt'
  implementation 'io.vertx:vertx-core'
  implementation 'io.vertx:vertx-unit'
  implementation 'io.vertx:vertx-web'
  implementation 'io.vertx:vertx-codegen'
  implementation 'com.fasterxml.jackson.core:jackson-databind'
  implementation 'io.tmio:tuweni-bytes'
  implementation 'io.tmio:tuweni-net'
  implementation 'io.tmio:tuweni-toml'
  implementation 'io.tmio:tuweni-units'
  implementation 'org.antlr:antlr4-runtime'
  implementation 'org.bouncycastle:bcprov-jdk18on'
  implementation 'org.springframework.security:spring-security-crypto'
  implementation 'org.xerial.snappy:snappy-java'
  implementation 'com.google.guava:guava'
  implementation 'io.vertx:vertx-core'
  implementation 'com.fasterxml.jackson.core:jackson-databind'
  implementation 'io.tmio:tuweni-bytes'
  implementation 'io.tmio:tuweni-units'
  implementation 'org.web3j:abi'

  annotationProcessor "org.immutables:value"
  implementation "org.immutables:value-annotations"

  runtimeOnly 'org.bouncycastle:bcpkix-jdk18on'
  runtimeOnly 'io.netty:netty-transport-native-epoll'
  runtimeOnly 'io.netty:netty-transport-native-kqueue'

  testImplementation project(':config')
  testImplementation project(path: ':config', configuration: 'testSupportArtifacts')
  testImplementation project(path: ':crypto:services', configuration: 'testSupportArtifacts')
  testImplementation project(path: ':ethereum:core', configuration: 'testArtifacts')
  testImplementation project(path: ':ethereum:core', configuration: 'testSupportArtifacts')
  testImplementation project(':services:kvstore')
  testImplementation project(':testutil')

  testResourceGeneration project(':besu')

  testImplementation 'com.squareup.okhttp3:okhttp'
  testImplementation 'io.vertx:vertx-auth-jwt'
  testImplementation 'io.vertx:vertx-junit5'
  testImplementation 'io.vertx:vertx-web-client'
  testImplementation 'org.apache.logging.log4j:log4j-core'
  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.junit.jupiter:junit-jupiter'

  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'

  testSupportImplementation 'org.bouncycastle:bcpkix-jdk18on'

  integrationTestImplementation project(':config')
  integrationTestImplementation project(path: ':config', configuration: 'testSupportArtifacts')
  integrationTestImplementation project(path: ':ethereum:core', configuration: 'testSupportArtifacts')
  integrationTestImplementation project(':services:kvstore')
  integrationTestImplementation project(':testutil')

  integrationTestImplementation 'org.assertj:assertj-core'
  integrationTestImplementation 'org.junit.jupiter:junit-jupiter'
  integrationTestImplementation 'org.mockito:mockito-core'
  integrationTestImplementation 'org.mockito:mockito-junit-jupiter'
  integrationTestImplementation 'org.testcontainers:testcontainers'

  integrationTestRuntimeOnly 'org.junit.jupiter:junit-jupiter'
}

artifacts { testSupportArtifacts testSupportJar }

tasks.register('generateTestBlockchain') {
  def srcFiles = 'src/test/resources/org/hyperledger/besu/ethereum/api/jsonrpc/trace/chain-data'
  def dataPath = "$buildDir/generated/data"
  def blocksBin = "$buildDir/resources/test/org/hyperledger/besu/ethereum/api/jsonrpc/trace/chain-data/blocks.bin"
  inputs.dir(srcFiles)
  outputs.file(blocksBin)
  dependsOn(configurations.testResourceGeneration)
  dependsOn(processTestResources)
  doLast {
    mkdir(dataPath)
    javaexec {
      main = 'org.hyperledger.besu.Besu'
      classpath = configurations.testResourceGeneration
      args = [
        "--logging=ERROR",
        "--data-path=$dataPath",
        "--genesis-file=$srcFiles/genesis.json",
        "blocks",
        "import",
        "--format=JSON",
        "--from=$srcFiles/blocks.json",
        "--start-time=1438269971"
      ]
    }
    javaexec {
      main = 'org.hyperledger.besu.Besu'
      classpath = configurations.testResourceGeneration
      args = [
        "--logging=ERROR",
        "--data-path=$dataPath",
        "--genesis-file=$srcFiles/genesis.json",
        "blocks",
        "export",
        "--format=RLP",
        "--start-block=0",
        "--to=$blocksBin"
      ]
    }
  }
}
test.dependsOn(generateTestBlockchain)
