{"request": {"id": 28, "jsonrpc": "2.0", "method": "eth_getProof", "params": ["******************************************", ["0x0000000000000000000000000000000000000000000000000000000000000347"], "pending"]}, "response": {"jsonrpc": "2.0", "id": 28, "result": {"accountProof": ["0xf891a084e747fd6c2e7ca25d6f219938c2d9e3014c9e86e3fc3fb0014dcef9d10b6815a097699db78357681938d946074cafa3355e921261ba0bd5081bff15c4f17d0c7f8080808080808080a07ac9345327d90f69293f207fd3dbad91418e38cf6c663c97e8cda89c32448509808080a093e09806aca0b1db534f5e6940b2d43a717c47bf4dfc342a104d9945e82a221d8080", "0xf86ba03b8ec137a2f5a74ec3a73144b552caad890b18b5f725872fa212fff6d4d565bab848f84680820140a0dd3fa56425f8195f314c22547243081293b3f5537cd98ed8f84d4fda3f9a515ba035178fc0de1e7fc754dbd07360e6f80bed818b9e51c62682f312442c4838ac47"], "address": "******************************************", "balance": "0x140", "codeHash": "0x35178fc0de1e7fc754dbd07360e6f80bed818b9e51c62682f312442c4838ac47", "nonce": "0x0", "storageHash": "0xdd3fa56425f8195f314c22547243081293b3f5537cd98ed8f84d4fda3f9a515b", "storageProof": [{"key": "0x0347", "value": "0x0", "proof": ["0xf8b18080a0c7f91ee5ea67d593275fcc773a02a5e5fb37b0625ca5c5cdc783483d43f2d7c280a06672614c102b0f02e747250bae452ed105110e25761c15a2a459afbd9eb8cf2e808080a0f671e05fab19f512b16fdcbc938f5fbbfbbc2fa67fecb575bb7721c221f71cf78080a0ba2fc36b0235d7ea299126bd4d404cb38456f32a6c236b422a3868f98c69eda1a0d77552655c7b63916586a73350b1933836f2378789613b548b90fa0a44125df180808080"]}]}}, "statusCode": 200}