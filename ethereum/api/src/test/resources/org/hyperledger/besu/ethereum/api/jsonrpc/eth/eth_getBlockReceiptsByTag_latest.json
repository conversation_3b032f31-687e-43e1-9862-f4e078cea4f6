{"request": {"id": 307, "jsonrpc": "2.0", "method": "eth_getBlockReceipts", "params": ["latest"]}, "response": {"jsonrpc": "2.0", "id": 307, "result": [{"blockHash": "0x71d59849ddd98543bdfbe8548f5eed559b07b8aaf196369f39134500eab68e53", "blockNumber": "0x20", "contractAddress": null, "cumulativeGasUsed": "0x5c99", "from": "******************************************", "gasUsed": "0x5c99", "effectiveGasPrice": "0x1", "logs": [{"address": "******************************************", "topics": ["0x0000000000000000000000000000000000000000000000000000000000000001", "0x000000000000000000000000a94f5374fce5edbc8e2a8697c15331677e6ebf0b", "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"], "data": "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe9000000000000000000000000000000000000000000000000000000000000002a", "blockNumber": "0x20", "transactionHash": "0xcef53f2311d7c80e9086d661e69ac11a5f3d081e28e02a9ba9b66749407ac310", "transactionIndex": "0x0", "blockHash": "0x71d59849ddd98543bdfbe8548f5eed559b07b8aaf196369f39134500eab68e53", "logIndex": "0x0", "removed": false}], "logsBloom": "0x00000000000000000000000800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040000000000000080000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000400000000000000000200000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000800000000040000000000000000000000000000000000000000010000000000000000000000000", "root": "0x6d54fb51c667e4cc4bd8f2633b675f831b35d7784a70b5df91cd58f593c5286e", "to": "******************************************", "transactionHash": "0xcef53f2311d7c80e9086d661e69ac11a5f3d081e28e02a9ba9b66749407ac310", "transactionIndex": "0x0", "type": "0x0"}]}, "statusCode": 200}