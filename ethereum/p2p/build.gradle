/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

apply plugin: 'java-library'

jar {
  archiveBaseName = 'besu-p2p'
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion()
      )
  }
}

dependencies {
  api 'org.slf4j:slf4j-api'

  implementation project(':crypto:services')
  implementation project(':datatypes')
  implementation project(':ethereum:core')
  implementation project(':ethereum:rlp')
  implementation project(':pki')
  implementation project(':metrics:core')
  implementation project(':nat')
  implementation project(':util')

  implementation 'com.google.guava:guava'
  implementation 'dnsjava:dnsjava'
  implementation 'io.netty:netty-transport-native-unix-common'
  implementation 'io.prometheus:simpleclient'
  implementation 'io.vertx:vertx-core'

  implementation 'io.tmio:tuweni-bytes'
  implementation 'io.tmio:tuweni-crypto'
  implementation('io.tmio:tuweni-devp2p') {
    exclude group:'ch.qos.logback', module:'logback-classic'
  }
  implementation('io.tmio:tuweni-dns-discovery'){
    exclude group:'ch.qos.logback', module:'logback-classic'
  }
  implementation 'io.tmio:tuweni-io'
  implementation 'io.tmio:tuweni-rlp'
  implementation 'io.tmio:tuweni-units'
  implementation 'org.jetbrains.kotlin:kotlin-stdlib'
  implementation 'org.owasp.encoder:encoder'
  implementation 'org.xerial.snappy:snappy-java'

  annotationProcessor "org.immutables:value"
  implementation "org.immutables:value-annotations"
  implementation('tech.pegasys.discovery:discovery') {
    // version conflicts, prefer ours
    exclude group: 'org.apache.tuweni', module:'tuweni-bytes'
    exclude group: 'org.apache.tuweni', module:'tuweni-crypto'
    exclude group: 'org.apache.tuweni', module:'tuweni-rlp'
    exclude group: 'org.apache.tuweni', module:'tuweni-units'
  }

  // test dependencies.
  testImplementation project(path: ':ethereum:core', configuration: 'testArtifacts')
  testImplementation project(path: ':ethereum:core', configuration: 'testSupportArtifacts')
  testImplementation project(path: ':crypto:services', configuration: 'testSupportArtifacts')
  testImplementation project(':testutil')

  testImplementation 'com.fasterxml.jackson.core:jackson-databind'
  testImplementation('io.pkts:pkts-core') {
    exclude group: 'io.pkts', module: 'pkts-sdp'
    exclude group: 'io.pkts', module: 'pkts-sip'
  }
  testImplementation 'io.vertx:vertx-codegen'
  testImplementation 'io.vertx:vertx-unit'
  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.awaitility:awaitility'
  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'
}
