/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

apply plugin: 'java-library'

jar {
  archiveBaseName = 'besu-eth'
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion()
      )
  }
}

configurations { testArtifacts }
task testJar(type: Jar) {
  archiveBaseName = "${project.name}-test"
  from sourceSets.test.output
}

dependencies {
  api 'org.slf4j:slf4j-api'
  api project(':util')

  annotationProcessor 'org.openjdk.jmh:jmh-generator-annprocess'
  implementation project(':config')
  implementation project(':datatypes')
  implementation project(':ethereum:core')
  implementation project(':ethereum:p2p')
  implementation project(':ethereum:permissioning')
  implementation project(':ethereum:rlp')
  implementation project(':ethereum:trie')
  implementation project(':evm')
  implementation project(':metrics:core')
  implementation project(':services:kvstore')
  implementation project(':services:pipeline')
  implementation project(':services:tasks')
  implementation project(':util')

  implementation 'com.google.guava:guava'
  implementation 'io.vertx:vertx-core'
  implementation 'org.apache.commons:commons-lang3'
  implementation 'io.tmio:tuweni-bytes'
  implementation 'io.tmio:tuweni-units'
  implementation 'io.tmio:tuweni-rlp'

  annotationProcessor "org.immutables:value"
  implementation "org.immutables:value-annotations"

  testImplementation project(':config')
  testImplementation project(path: ':config', configuration: 'testSupportArtifacts')
  testImplementation project(':crypto:services')
  testImplementation project(path: ':crypto:services', configuration: 'testSupportArtifacts')
  testImplementation project(path: ':ethereum:core', configuration: 'testArtifacts')
  testImplementation project(path: ':ethereum:core', configuration: 'testSupportArtifacts')
  testImplementation project(':ethereum:mock-p2p')
  testImplementation project(':ethereum:referencetests')
  testImplementation project(path: ':metrics:core', configuration: 'testSupportArtifacts')
  testImplementation project(':testutil')

  testImplementation 'com.fasterxml.jackson.core:jackson-databind'
  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.awaitility:awaitility'
  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'
  testImplementation 'org.openjdk.jol:jol-core'

  testSupportImplementation 'org.mockito:mockito-core'
  testSupportImplementation project(':testutil')
  testSupportImplementation project(path: ':ethereum:core', configuration: 'testArtifacts')
  testSupportImplementation project(path: ':ethereum:core', configuration: 'testSupportArtifacts')

  jmhImplementation project(':besu')
  jmhImplementation project(path: ':ethereum:core', configuration: 'testSupportArtifacts')
  jmhImplementation project(':plugins:rocksdb')
}

artifacts {
  testArtifacts testJar
  testSupportArtifacts testSupportJar
}
