#Whether to use 'space', 'tab' or 'mixed' (both) characters for indentation. 
#The default value is 'tab'.
org.eclipse.jdt.core.formatter.tabulation.char=space

#Number of spaces used for indentation in case 'space' characters 
#have been selected. The default value is 4.
org.eclipse.jdt.core.formatter.tabulation.size=2

#Number of spaces used for indentation in case 'mixed' characters 
#have been selected. The default value is 4.
org.eclipse.jdt.core.formatter.indentation.size=1

#Whether or not indentation characters are inserted into empty lines.
#The default value is 'true'.
org.eclipse.jdt.core.formatter.indent_empty_lines=false

#Number of spaces used for multiline indentation.
#The default value is 2.
groovy.formatter.multiline.indentation=1

#Length after which list are considered too long. These will be wrapped.
#The default value is 30.
groovy.formatter.longListLength=30

#Whether opening braces position shall be the next line.
#The default value is 'same'.
groovy.formatter.braces.start=same

#Whether closing braces position shall be the next line.
#The default value is 'next'.
groovy.formatter.braces.end=next

#Remove unnecessary semicolons. The default value is 'false'. 
groovy.formatter.remove.unnecessary.semicolons=false

org.eclipse.jdt.core.formatter.brace_position_for_anonymous_type_declaration=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_method_declaration=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_block=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_constructor_declaration=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_lambda_body=end_of_line
org.eclipse.jdt.core.formatter.insert_new_line_before_closing_brace_in_array_initializer=do not insert
org.eclipse.jdt.core.formatter.brace_position_for_enum_declaration=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_block_in_case=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_annotation_type_declaration=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_switch=end_of_line
org.eclipse.jdt.core.formatter.insert_new_line_after_opening_brace_in_array_initializer=do not insert
org.eclipse.jdt.core.formatter.brace_position_for_array_initializer=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_enum_constant=end_of_line
org.eclipse.jdt.core.formatter.brace_position_for_type_declaration=end_of_line

org.eclipse.jdt.core.formatter.insert_new_line_at_end_of_file_if_missing=insert

# Formatter can be buggy in CI
ignoreFormatterProblems=true
