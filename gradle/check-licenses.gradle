/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Check that the licenses of our 3rd parties are in our acceptedLicenses list.
 *
 * run it with "gradle checkLicenses"
 *
 * To add new accepted licenses you need to update this script.
 * Some products may be available with multiple licenses. In this case you must update
 *  this script to add it in the downloadLicenses#licenses.
 */

// Some parts of this code comes from Zipkin/https://github.com/openzipkin/zipkin/pull/852
// Zipkin itself is under Apache License.

/**
 * The lists of the license we accept.
 */
ext.acceptedLicenses = [
  'Apache License, Version 2.0',
  'BSD 3-Clause',
  'BSD License',
  'Bouncy Castle Licence',
  'CC0 1.0 Universal License',
  'Common Development and Distribution License 1.0',
  'Eclipse Public License 1.0',
  'Eclipse Public License - v 2.0',
  'MIT License',
  'Mozilla Public License 1.0',
  'Mozilla Public License Version 1.1',
  'Mozilla Public License Version 2.0',
  'Oracle Free Use Terms and Conditions (FUTC)',
  'Public Domain (CC0) License 1.0',
  'Public Domain',
  'Unicode/ICU License',
  'New BSD License'
]*.toLowerCase()

/**
 * This is the configuration we need for our licenses plugin: 'com.github.hierynomus.license'
 * This plugin generates a list of dependencies.
 */
downloadLicenses {
  includeProjectDependencies = false
  reportByDependency = false
  reportByLicenseType = true
  dependencyConfiguration = 'runtimeClasspath'
  excludeDependencies = [
    // only used for static analysis, not actually shipped
    'com.google.code.findbugs:jFormatString:3.0.0',
    'com.google.errorprone:javac:.*',
    'org.checkerframework:dataflow-shaded:.*',
    'org.checkerframework:dataflow-errorprone:.*',
  ]

  ext.apache = license('Apache License, Version 2.0', 'http://opensource.org/licenses/Apache-2.0')
  ext.bsd = license('BSD License', 'http://www.opensource.org/licenses/bsd-license.php')
  ext.bsd3Clause = license('BSD 3-Clause', 'http://opensource.org/licenses/BSD-3-Clause')
  ext.cc0 = license('Public Domain (CC0) License 1.0', 'https://creativecommons.org/publicdomain/zero/1.0')
  ext.cddl = license('Common Development and Distribution License 1.0', 'http://opensource.org/licenses/CDDL-1.0')
  ext.epl1 = license('Eclipse Public License 1.0', 'https://www.eclipse.org/legal/epl-v10.html')
  ext.epl2 = license('Eclipse Public License - v 2.0', 'https://www.eclipse.org/legal/epl-v20.html')
  ext.mit = license('MIT License', 'http://www.opensource.org/licenses/mit-license.php')
  aliases = [
    (apache) : [
      'Apache 2',
      'Apache 2.0',
      'Apache License 2.0',
      'Apache Software License - Version 2.0',
      'Apache Software License 2.0',
      'Apache v2',
      'Apache-2.0',
      'The Apache License, Version 2.0',
      'The Apache Software License, Version 2.0',
      'The Apache Software License, version 2.0',
      license('', 'http://www.apache.org/licenses/LICENSE-2.0.txt')
    ],
    (bsd) : [
      'BSD',
      'BSD-2-Clause',
      'BSD licence',
      'The BSD License',
    ],
    (bsd3Clause) : [
      '3-Clause BSD License',
      'BSD 3-Clause "New" or "Revised" License (BSD-3-Clause)',
      'BSD 3-Clause',
      'BSD Licence 3',
      'BSD License 3',
      'BSD-3-Clause',
      'Eclipse Distribution License - v 1.0',
      'Eclipse Distribution License (New BSD License)',
      'EDL 1.0',
      'New BSD License',
      'The BSD 3-Clause License',
      license('', 'http://asm.ow2.org/license.html'),
      license('BSD 3-Clause', 'http://www.scala-lang.org/license.html'),
      license('BSD 3-clause', 'http://opensource.org/licenses/BSD-3-Clause'),
    ],
    (cc0) : [
      'CC0'
    ],
    (cddl) : [
      'CDDL 1.0',
      'CDDL 1.1',
      'Common Development and Distribution License (CDDL) v1.0',
      'CDDL + GPLv2 with classpath exception',
    ],
    (epl1) : [
      'Eclipse Public License - Version 1.0',
      'Eclipse Public License - v 1.0',
      'Eclipse Public License 1.0 ',
    ],
    (epl2) : [
      'Eclipse Public License - v 2.0',
      'Eclipse Public License version 2.0'
    ],
    (mit) : [
      'MIT',
      'The MIT License',
      'The MIT License (MIT)',
    ],
  ]
  licenses = [
    (group('besu')) : apache,
    (group('besu.ethereum.api')) : apache,
    (group('besu.consensus')) : apache,
    (group('besu.ethereum')) : apache,
    (group('besu.metrics')) : apache,
    (group('besu.plugins')) : apache,
    (group('besu.services')) : apache,

    // JNA is dual licensed under Apache v2.0 or LGPL 2 licenses
    // Explicitly declare that we are using the Apache v2.0 license
    (group('net.java.dev.jna')) : apache,

    // RocksDB is dual licensed under Apache v2.0 or GPL 2 licenses
    // Explicitly declare that we are using the Apache v2.0 license
    (group('org.rocksdb')) : apache,

    // Logback is dual licensed under EPL v1.0 or LGPL v2.1
    // Explicitly declare that we are using the EPL v1.0 license
    (group('ch.qos.logback')) : epl1,

    // JavaMail, JAXB, Glassfish and Java Servlet are dual licensed under CDDL 1.1 or GPL 2 w/ Classpath Exception
    // Explicitly declare that we are using the CDDL 1.1 license
    (group('com.sun.mail')) : cddl,
    (group('javax.servlet')) : cddl,
    (group('javax.xml.bind')) : cddl,
    (group('org.glassfish')) : cddl,

    // javax.persistence is dual licensed under EPL 1.0 and EDL 1.0.
    // Explicitly declare that we are using the EPL 1.0 license
    (group('javax.persistence')) : epl1,

    // jnr-posix is released under a tri EPL v2.0/GPL/LGPL license
    'com.github.jnr:jnr-posix:3.0.47' : epl2,
    'com.github.jnr:jnr-posix:3.1.15' : epl2,

    // io.netty:netty-tcnative-boringssl-static license markings are not machine readable.
    // io.netty:netty-tcnative-classes license markings are not machine readable.
    // Explicitly state Apache 2 License for license scanning.
    'io.netty:netty-tcnative-boringssl-static:2.0.46.Final' : apache,
    'io.netty:netty-tcnative-classes:2.0.46.Final' : apache,

    // ANTLR license markings are not all machine readable.
    // Explicitly state BSD License for license scanning.
    'org.antlr:antlr-runtime:3.5.2': bsd,
    'org.antlr:ST4:4.3.1': bsd,
  ]
}

task lazyDownloadLicenses() {
  if (gradle.startParameter.taskNames.contains('clean') || !file("$rootProject.buildDir/reports/license/license-dependency.xml").exists()) {
    dependsOn ':downloadLicenses'
  }
}

task checkLicenses {
  description "Verify that all dependencies use accepted licenses."
  dependsOn ':lazyDownloadLicenses'

  def bads = ""
  doLast {
    def xml = new XmlParser().parse("${rootProject.buildDir}/reports/license/license-dependency.xml")
    xml.each { license ->
      if (!acceptedLicenses.contains((license.@name).toLowerCase())) {
        def depStrings = []
        license.dependency.each { depStrings << it.text() }
        bads = bads + depStrings + " =>  ${license.@name} \n"
      }
    }
    if (bads != "") {
      throw new GradleException("Some 3rd parties are using licenses not in our accepted licenses list:\n" +
        bads +
        "If it's a license acceptable for us, add it in the file check-licenses.gradle\n" +
        "Be careful, some 3rd parties may accept multiple licenses.\n" +
        "In this case, select the one you want to use by changing downloadLicenses.licenses\n"
      )
    }
  }
}
