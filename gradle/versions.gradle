/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

dependencyManagement {
  dependencies {
    applyMavenExclusions = false

    dependencySet(group: 'org.antlr', version: '4.11.1') {
      entry 'antlr4'
      entry 'antlr4-runtime'
    }

    dependencySet(group:'com.fasterxml.jackson.core', version:'2.16.1') {
      entry 'jackson-databind'
      entry 'jackson-datatype'
      entry 'jackson-datatype-jdk8'
    }

    dependency 'com.github.ben-manes.caffeine:caffeine:3.1.8'

    dependency 'com.github.oshi:oshi-core:6.4.10'

    dependency 'com.google.auto.service:auto-service:1.1.1'

    dependencySet(group: 'com.google.dagger', version: '2.50') {
      entry'dagger-compiler'
      entry'dagger'
    }

    dependency 'org.hyperledger.besu:besu-errorprone-checks:1.0.0'

    dependency 'com.google.guava:guava:33.0.0-jre'

    dependency 'com.graphql-java:graphql-java:21.3'

    dependency 'com.splunk.logging:splunk-library-javalogging:1.11.8'

    dependency 'com.squareup.okhttp3:okhttp:4.12.0'

    dependency 'commons-codec:commons-codec:1.16.0'

    dependency 'commons-io:commons-io:2.15.1'

    dependency 'dnsjava:dnsjava:3.5.3'

    dependencySet(group: 'info.picocli', version: '4.7.5') {
      entry 'picocli'
      entry 'picocli-codegen'
    }

    dependencySet(group: 'io.grpc', version: '1.60.1') {
      entry 'grpc-all'
      entry 'grpc-core'
      entry 'grpc-netty'
      entry 'grpc-stub'
    }

    dependency 'io.kubernetes:client-java:18.0.1'

    dependency 'io.netty:netty-all:4.1.104.Final'
    dependency 'io.netty:netty-tcnative-boringssl-static:2.0.62.Final'
    dependency group: 'io.netty', name: 'netty-transport-native-epoll', version:'4.1.104.Final', classifier: 'linux-x86_64'
    dependency group: 'io.netty', name: 'netty-transport-native-kqueue', version:'4.1.104.Final', classifier: 'osx-x86_64'
    dependency 'io.netty:netty-transport-native-unix-common:4.1.104.Final'

    dependency 'io.opentelemetry:opentelemetry-api:1.33.0'
    dependency 'io.opentelemetry:opentelemetry-exporter-otlp:1.33.0'
    dependency 'io.opentelemetry:opentelemetry-extension-trace-propagators:1.33.0'
    dependency 'io.opentelemetry:opentelemetry-sdk-metrics:1.33.0'
    dependency 'io.opentelemetry:opentelemetry-sdk-trace:1.33.0'
    dependency 'io.opentelemetry:opentelemetry-sdk:1.33.0'
    dependency 'io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.33.0'
    dependency 'io.opentelemetry.instrumentation:opentelemetry-okhttp-3.0:1.32.0-alpha'
    dependency 'io.opentelemetry.proto:opentelemetry-proto:1.0.0-alpha'
    dependency 'io.opentelemetry.semconv:opentelemetry-semconv:1.23.1-alpha'

    dependency 'io.opentracing.contrib:opentracing-okhttp3:3.0.0'
    dependency 'io.opentracing:opentracing-api:0.33.0'
    dependency 'io.opentracing:opentracing-util:0.33.0'

    dependency 'io.pkts:pkts-core:3.0.10'

    dependencySet(group: 'io.prometheus', version: '0.16.0') {
      entry 'simpleclient'
      entry 'simpleclient_common'
      entry 'simpleclient_hotspot'
      entry 'simpleclient_pushgateway'
      entry 'simpleclient_guava'
    }

    dependency 'io.reactivex.rxjava2:rxjava:2.2.21'

    dependencySet(group: 'io.tmio', version: '2.4.2') {
      entry 'tuweni-bytes'
      entry 'tuweni-config'
      entry 'tuweni-concurrent'
      entry 'tuweni-crypto'
      entry 'tuweni-devp2p'
      entry 'tuweni-dns-discovery'
      entry 'tuweni-io'
      entry 'tuweni-net'
      entry 'tuweni-rlp'
      entry 'tuweni-toml'
      entry 'tuweni-units'
    }

    dependencySet(group: 'io.vertx', version: '4.5.4') {
      entry 'vertx-auth-jwt'
      entry 'vertx-codegen'
      entry 'vertx-core'
      entry 'vertx-junit5'
      entry 'vertx-unit'
      entry 'vertx-web'
      entry 'vertx-web-client'
      entry 'vertx-auth-jwt'
    }

    dependency 'junit:junit:4.13.2'

    dependency 'net.java.dev.jna:jna:5.14.0'

    dependency 'org.apache.commons:commons-compress:1.26.0'
    dependency 'org.apache.commons:commons-lang3:3.14.0'
    dependency 'org.apache.commons:commons-text:1.11.0'

    dependencySet(group: 'org.apache.logging.log4j', version: '2.22.1') {
      entry 'log4j-api'
      entry 'log4j-core'
      entry 'log4j-jul'
      entry 'log4j-slf4j2-impl'
    }

    dependency 'org.assertj:assertj-core:3.25.1'

    dependency 'org.awaitility:awaitility:4.2.0'

    dependencySet(group: 'org.bouncycastle', version: '1.77') {
      entry'bcpkix-jdk18on'
      entry'bcprov-jdk18on'
    }

    dependency 'org.fusesource.jansi:jansi:2.4.1'
    dependency 'org.openjdk.jol:jol-core:0.17'
    dependency 'tech.pegasys:jc-kzg-4844:1.0.0'

    dependencySet(group: 'org.hyperledger.besu', version: '0.8.2') {
      entry 'arithmetic'
      entry 'ipa-multipoint'
      entry 'bls12-381'
      entry 'secp256k1'
      entry 'secp256r1'
      entry 'blake2bf'
    }

    dependencySet(group: 'org.immutables', version: '2.10.0') {
      entry 'value-annotations'
      entry 'value'
    }

    dependency 'org.java-websocket:Java-WebSocket:1.5.5'

    dependency 'org.jetbrains.kotlin:kotlin-stdlib:1.9.22'

    dependencySet(group: 'org.junit.jupiter', version: '5.10.1') {
      entry 'junit-jupiter'
      entry 'junit-jupiter-api'
      entry 'junit-jupiter-engine'
      entry 'junit-jupiter-params'
    }

    dependency 'org.junit.platform:junit-platform-runner:1.9.2'

    dependency 'org.junit.vintage:junit-vintage-engine:5.10.1'

    dependencySet(group: 'org.jupnp', version:'2.7.1') {
      entry 'org.jupnp.support'
      entry 'org.jupnp'
    }

    dependencySet(group: 'org.mockito', version:'5.8.0') {
      entry 'mockito-core'
      entry 'mockito-junit-jupiter'
    }

    dependencySet(group: 'org.openjdk.jmh', version:'1.36') {
      entry 'jmh-core'
      entry 'jmh-generator-annprocess'
    }

    dependency 'org.owasp.encoder:encoder:1.2.3'

    dependency 'org.rocksdb:rocksdbjni:8.3.2' // 8.9.1 causes a bug with a FOREST canary

    dependencySet(group: 'org.slf4j', version:'2.0.10') {
      entry 'slf4j-api'
      entry 'slf4j-nop'
    }

    dependency 'org.springframework.security:spring-security-crypto:6.2.1'

    dependency 'org.testcontainers:testcontainers:1.19.3'

    dependency 'org.web3j:quorum:4.9.5'
    dependencySet(group: 'org.web3j', version: '4.10.3') {
      entry 'abi'
      entry 'besu'
      entry 'core'
      entry 'crypto'
    }

    dependencySet(group: 'org.wiremock', version: '3.3.1') {
      entry 'wiremock'
    }

    dependency 'org.xerial.snappy:snappy-java:1.1.10.5'

    dependency 'org.yaml:snakeyaml:2.0'

    dependency 'org.apache.maven:maven-artifact:3.9.6'

    dependency 'tech.pegasys.discovery:discovery:22.12.0'
  }
}
