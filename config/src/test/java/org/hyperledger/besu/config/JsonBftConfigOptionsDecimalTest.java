/*
 * Copyright Hyperledger Besu Contributors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.config;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.Test;

public class JsonBftConfigOptionsDecimalTest {

  @Test
  public void shouldParseDecimalBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", 0.5);
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThat(configOptions.getBlockPeriodSeconds()).isEqualTo(0.5);
  }

  @Test
  public void shouldParseStringDecimalBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", "0.5");
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThat(configOptions.getBlockPeriodSeconds()).isEqualTo(0.5);
  }

  @Test
  public void shouldParseIntegerBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", 2);
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThat(configOptions.getBlockPeriodSeconds()).isEqualTo(2.0);
  }

  @Test
  public void shouldParseStringIntegerBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", "2");
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThat(configOptions.getBlockPeriodSeconds()).isEqualTo(2.0);
  }

  @Test
  public void shouldRejectZeroBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", 0);
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThatThrownBy(configOptions::getBlockPeriodSeconds)
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("should be a positive number");
  }

  @Test
  public void shouldRejectNegativeBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", -1.5);
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThatThrownBy(configOptions::getBlockPeriodSeconds)
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("should be a positive number");
  }

  @Test
  public void shouldRejectInvalidStringBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", "invalid");
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThatThrownBy(configOptions::getBlockPeriodSeconds)
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("should be a positive number");
  }

  @Test
  public void shouldUseDefaultWhenBlockPeriodSecondsNotSpecified() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThat(configOptions.getBlockPeriodSeconds()).isEqualTo(1.0);
  }

  @Test
  public void shouldParseVerySmallDecimalBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", 0.1);
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThat(configOptions.getBlockPeriodSeconds()).isEqualTo(0.1);
  }

  @Test
  public void shouldParseLargeDecimalBlockPeriodSeconds() {
    final ObjectNode configNode = JsonUtil.createEmptyObjectNode();
    configNode.put("blockperiodseconds", 123.456);
    
    final JsonBftConfigOptions configOptions = new JsonBftConfigOptions(configNode);
    
    assertThat(configOptions.getBlockPeriodSeconds()).isEqualTo(123.456);
  }
}
